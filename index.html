<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nave Espacial 3D - Evade <PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 200;
            display: none;
        }
        
        #instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 14px;
        }
        
        button {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 5px;
        }
        
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="ui">
            <div>Puntuación: <span id="score">0</span></div>
            <div>Velocidad: <span id="speed">1 (Nivel 1)</span></div>
            <div>Obstáculos: <span id="obstacles">0</span></div>
        </div>
        
        <div id="instructions">
            <div>🖱️ Click para subir la nave</div>
            <div>⬇️ Suelta para bajar</div>
            <div>🎯 Evita los obstáculos rojos</div>
        </div>
        
        <div id="gameOver">
            <h2>¡Juego Terminado!</h2>
            <p>Puntuación Final: <span id="finalScore">0</span></p>
            <button onclick="restartGame()">Jugar de Nuevo</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // Variables del juego
        let scene, camera, renderer, spaceship, obstacles = [];
        let gameRunning = true;
        let score = 0;
        let gameSpeed = 1;
        let mousePressed = false;
        let spaceshipVelocityY = 0;
        let spaceshipTargetY = 0;
        let lastObstacleHeight = 0; // Para intercalar alturas
        let obstaclesPassed = 0; // Contador de obstáculos superados
        let difficultyLevel = 1; // Nivel de dificultad actual
        
        // Configuración del juego
        const SPACESHIP_SPEED = 0.15; // Más sensible a los clicks
        const GRAVITY = 0.008; // Caída más rápida
        const OBSTACLE_SPEED = 0.1;
        const SPAWN_RATE = 0.02;
        
        // Inicializar el juego
        function init() {
            // Crear escena
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x000033, 10, 100);
            
            // Crear cámara (vista en tercera persona centrada)
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 10);
            camera.lookAt(0, 0, 0);
            
            // Crear renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000033);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('gameContainer').appendChild(renderer.domElement);
            
            // Crear nave espacial
            createSpaceship();
            
            // Crear luces
            createLights();
            
            // Crear estrellas de fondo
            createStars();
            
            // Event listeners
            setupEventListeners();
            
            // Iniciar loop del juego
            animate();
        }
        
        function createSpaceship() {
            const spaceshipGroup = new THREE.Group();
            
            // Cuerpo principal de la nave
            const bodyGeometry = new THREE.ConeGeometry(0.3, 1.5, 8);
            const bodyMaterial = new THREE.MeshPhongMaterial({ color: 0x4CAF50 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.rotation.x = Math.PI / 2;
            body.castShadow = true;
            spaceshipGroup.add(body);
            
            // Alas de la nave
            const wingGeometry = new THREE.BoxGeometry(1.5, 0.1, 0.3);
            const wingMaterial = new THREE.MeshPhongMaterial({ color: 0x2E7D32 });
            const leftWing = new THREE.Mesh(wingGeometry, wingMaterial);
            leftWing.position.set(0, 0, -0.3);
            leftWing.castShadow = true;
            spaceshipGroup.add(leftWing);
            
            // Motor/propulsor
            const engineGeometry = new THREE.CylinderGeometry(0.15, 0.2, 0.5, 8);
            const engineMaterial = new THREE.MeshPhongMaterial({ color: 0xFF5722 });
            const engine = new THREE.Mesh(engineGeometry, engineMaterial);
            engine.position.set(0, 0, -0.8);
            engine.rotation.x = Math.PI / 2;
            engine.castShadow = true;
            spaceshipGroup.add(engine);
            
            spaceship = spaceshipGroup;
            spaceship.position.set(0, 0, 0); // Centrado en la pantalla
            scene.add(spaceship);
        }
        
        function createLights() {
            // Luz ambiental
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);
            
            // Luz direccional
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
        }
        
        function createStars() {
            const starGeometry = new THREE.BufferGeometry();
            const starMaterial = new THREE.PointsMaterial({ color: 0xFFFFFF, size: 0.1 });
            
            const starVertices = [];
            for (let i = 0; i < 1000; i++) {
                const x = (Math.random() - 0.5) * 200;
                const y = (Math.random() - 0.5) * 200;
                const z = (Math.random() - 0.5) * 200;
                starVertices.push(x, y, z);
            }
            
            starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
            const stars = new THREE.Points(starGeometry, starMaterial);
            scene.add(stars);
        }
        
        function createObstacle() {
            const obstacleGroup = new THREE.Group();

            // Intercalar altura: pegados arriba o abajo
            let yPosition, isTop;
            if (lastObstacleHeight <= 0) {
                yPosition = 0; // Posición base para diente superior
                isTop = true;
                lastObstacleHeight = 1;
            } else {
                yPosition = 0; // Posición base para diente inferior
                isTop = false;
                lastObstacleHeight = -1;
            }

            // Crear diente de cocodrilo (forma triangular puntiaguda)
            const toothHeight = 8;
            const toothWidth = 3;

            let geometry;
            if (isTop) {
                // Diente superior (punta hacia abajo)
                geometry = new THREE.ConeGeometry(toothWidth, toothHeight, 4);
                yPosition = 4; // Posicionado en la parte superior
            } else {
                // Diente inferior (punta hacia arriba)
                geometry = new THREE.ConeGeometry(toothWidth, toothHeight, 4);
                yPosition = -4; // Posicionado en la parte inferior
            }

            const material = new THREE.MeshPhongMaterial({
                color: 0xFF0000,
                transparent: false,
                opacity: 1.0
            });
            const obstacle = new THREE.Mesh(geometry, material);
            obstacle.castShadow = true;
            obstacle.receiveShadow = true;

            // Rotar el diente inferior para que apunte hacia arriba
            if (!isTop) {
                obstacle.rotation.x = Math.PI; // Rotar 180 grados
            }

            obstacleGroup.add(obstacle);

            // Posición inicial
            obstacleGroup.position.set(0, yPosition, -20);

            // Propiedades para efectos visuales
            const difficultyMultiplier = difficultyLevel;
            obstacleGroup.userData = {
                baseScale: 1,
                scaleDirection: Math.random() > 0.5 ? 1 : -1,
                scaleSpeed: (0.01 + Math.random() * 0.02) * difficultyMultiplier,
                rotationSpeed: {
                    x: 0, // Sin rotación en X para mantener orientación
                    y: (Math.random() - 0.5) * 0.01 * difficultyMultiplier,
                    z: (Math.random() - 0.5) * 0.01 * difficultyMultiplier
                },
                minScale: 0.5,
                maxScale: 1.8 + (difficultyLevel * 0.3),
                isTop: isTop
            };

            scene.add(obstacleGroup);
            obstacles.push(obstacleGroup);
        }

        function setupEventListeners() {
            // Mouse events
            document.addEventListener('mousedown', () => {
                if (gameRunning) {
                    mousePressed = true;
                }
            });

            document.addEventListener('mouseup', () => {
                mousePressed = false;
            });

            // Resize event
            window.addEventListener('resize', onWindowResize);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function updateSpaceship() {
            if (!gameRunning) return;

            // Aplicar movimiento más directo y sensible
            if (mousePressed) {
                spaceshipVelocityY += SPACESHIP_SPEED; // Subida más sensible
            } else {
                spaceshipVelocityY -= GRAVITY; // Caída más rápida
            }

            // Limitar velocidad (más rango para movimiento más dramático)
            spaceshipVelocityY = Math.max(-0.2, Math.min(0.15, spaceshipVelocityY));

            // Actualizar posición directamente
            spaceship.position.y += spaceshipVelocityY;

            // Limitar posición en pantalla
            spaceship.position.y = Math.max(-4, Math.min(4, spaceship.position.y));

            // Rotación basada en velocidad
            spaceship.rotation.z = -spaceshipVelocityY * 3;

            // Efectos visuales en la nave (pulsación sutil)
            const time = Date.now() * 0.005;
            spaceship.scale.setScalar(1 + Math.sin(time) * 0.05);
        }

        function updateObstacles() {
            if (!gameRunning) return;

            // Actualizar obstáculos existentes
            for (let i = obstacles.length - 1; i >= 0; i--) {
                const obstacle = obstacles[i];

                // Mover obstáculo hacia la cámara (efecto de acercamiento)
                obstacle.position.z += OBSTACLE_SPEED * gameSpeed;

                // Efectos visuales de escala (agrandar/achicar para confundir)
                const userData = obstacle.userData;
                userData.baseScale += userData.scaleDirection * userData.scaleSpeed;

                // Cambiar dirección de escala cuando llegue a límites (más extremos)
                if (userData.baseScale > userData.maxScale) {
                    userData.scaleDirection = -1;
                } else if (userData.baseScale < userData.minScale) {
                    userData.scaleDirection = 1;
                }

                // Aplicar efectos visuales al diente
                if (obstacle.children && obstacle.children.length > 0) {
                    const tooth = obstacle.children[0];
                    if (tooth) {
                        // Aplicar escala para efecto visual
                        tooth.scale.setScalar(userData.baseScale);

                        // Rotación sutil (mantener orientación del diente)
                        tooth.rotation.y += userData.rotationSpeed.y;
                        tooth.rotation.z += userData.rotationSpeed.z;

                        // Mantener orientación correcta del diente
                        if (!userData.isTop) {
                            tooth.rotation.x = Math.PI; // Diente inferior siempre apunta hacia arriba
                        }
                    }
                }

                // Eliminar obstáculos que pasaron la cámara
                if (obstacle.position.z > 15) {
                    scene.remove(obstacle);
                    obstacles.splice(i, 1);
                    score += 10;
                    obstaclesPassed++;

                    // Aumentar dificultad cada 6 obstáculos (3 arriba + 3 abajo)
                    if (obstaclesPassed % 6 === 0) {
                        difficultyLevel++;
                        gameSpeed *= 1.3; // Aumentar velocidad x1.3 cada nivel
                        console.log(`¡Nivel de dificultad ${difficultyLevel}! Velocidad: ${gameSpeed.toFixed(2)}`);
                    }
                }

                // Detectar colisiones cuando el obstáculo está cerca
                if (obstacle.position.z > 8 && obstacle.position.z < 12) {
                    // Verificar colisión usando posiciones directas
                    const shipPos = spaceship.position;
                    const obstaclePos = obstacle.position;

                    // Distancia entre nave y obstáculo
                    const distance = Math.sqrt(
                        Math.pow(shipPos.x - obstaclePos.x, 2) +
                        Math.pow(shipPos.y - obstaclePos.y, 2) +
                        Math.pow(shipPos.z - obstaclePos.z, 2)
                    );

                    // Colisión más precisa para dientes de cocodrilo
                    if (distance < 3.0) {
                        console.log("¡COLISIÓN CON DIENTE! Distancia:", distance.toFixed(2));
                        gameOver();
                    }
                }
            }

            // Crear nuevos obstáculos
            if (Math.random() < SPAWN_RATE * gameSpeed) {
                createObstacle();
            }
        }

        // Función simplificada de colisión (ya no se usa, pero la mantengo por compatibilidad)
        function checkCollisionWithHitbox(ship, hitbox, obstaclePosition) {
            return false; // Ahora usamos detección de distancia directa
        }

        function updateUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('speed').textContent = `${gameSpeed.toFixed(1)} (Nivel ${difficultyLevel})`;
            document.getElementById('obstacles').textContent = obstaclesPassed;
        }

        function gameOver() {
            gameRunning = false;
            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameOver').style.display = 'block';
        }

        function restartGame() {
            // Resetear variables
            gameRunning = true;
            score = 0;
            gameSpeed = 1;
            spaceshipVelocityY = 0;
            obstaclesPassed = 0;
            difficultyLevel = 1;

            // Limpiar obstáculos
            obstacles.forEach(obstacle => scene.remove(obstacle));
            obstacles = [];

            // Resetear posición de la nave
            spaceship.position.set(0, 0, 0);
            spaceship.rotation.z = 0;
            spaceship.scale.setScalar(1);
            spaceshipVelocityY = 0;
            lastObstacleHeight = 0;

            // Ocultar pantalla de game over
            document.getElementById('gameOver').style.display = 'none';
        }

        function animate() {
            requestAnimationFrame(animate);

            updateSpaceship();
            updateObstacles();
            updateUI();

            renderer.render(scene, camera);
        }

        // Inicializar el juego cuando se carga la página
        window.addEventListener('load', init);
    </script>
</body>
</html>
