<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nave Espacial 3D - Evade <PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 18px;
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 200;
            display: none;
        }
        
        #instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 14px;
        }
        
        button {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 5px;
        }
        
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="ui">
            <div>Puntuación: <span id="score">0</span></div>
            <div>Velocidad: <span id="speed">1 (Nivel 1)</span></div>
            <div>Obstáculos: <span id="obstacles">0</span></div>
        </div>
        
        <div id="instructions">
            <div>🖱️ Click para subir la nave (cuesta levantarla)</div>
            <div>⬇️ Suelta para bajar suavemente</div>
            <div>🦷 Pasa por el espacio entre los dientes</div>
            <div>📈 Cada 6 obstáculos: dientes más pequeños</div>
        </div>
        
        <div id="gameOver">
            <h2>¡Juego Terminado!</h2>
            <p>Puntuación Final: <span id="finalScore">0</span></p>
            <button onclick="restartGame()">Jugar de Nuevo</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // Variables del juego
        let scene, camera, renderer, spaceship, obstacles = [];
        let gameRunning = true;
        let score = 0;
        let gameSpeed = 1;
        let mousePressed = false;
        let spaceshipVelocityY = 0;
        let spaceshipTargetY = 0;
        let lastObstacleHeight = 0; // Para intercalar alturas
        let obstaclesPassed = 0; // Contador de obstáculos superados
        let difficultyLevel = 1; // Nivel de dificultad actual
        
        // Configuración del juego
        const SPACESHIP_SPEED = 0.08; // Subida más lenta (cuesta levantarla)
        const GRAVITY = 0.006; // Caída más suave
        const OBSTACLE_SPEED = 0.1;
        const SPAWN_RATE = 0.02;
        
        // Inicializar el juego
        function init() {
            // Crear escena con ambiente más dramático
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x001122, 8, 80); // Niebla más densa y azulada
            
            // Crear cámara (vista en tercera persona centrada)
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 10);
            camera.lookAt(0, 0, 0);
            
            // Crear renderer con mejor calidad visual
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x001122); // Fondo más dramático
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.gammaOutput = true;
            renderer.gammaFactor = 2.2;
            document.getElementById('gameContainer').appendChild(renderer.domElement);
            
            // Crear nave espacial
            createSpaceship();
            
            // Crear luces
            createLights();
            
            // Crear estrellas de fondo
            createStars();
            
            // Event listeners
            setupEventListeners();
            
            // Iniciar loop del juego
            animate();
        }
        
        function createSpaceship() {
            const spaceshipGroup = new THREE.Group();
            
            // Cuerpo principal de la nave
            const bodyGeometry = new THREE.ConeGeometry(0.3, 1.5, 8);
            const bodyMaterial = new THREE.MeshPhongMaterial({ color: 0x4CAF50 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.rotation.x = Math.PI / 2;
            body.castShadow = true;
            spaceshipGroup.add(body);
            
            // Alas de la nave
            const wingGeometry = new THREE.BoxGeometry(1.5, 0.1, 0.3);
            const wingMaterial = new THREE.MeshPhongMaterial({ color: 0x2E7D32 });
            const leftWing = new THREE.Mesh(wingGeometry, wingMaterial);
            leftWing.position.set(0, 0, -0.3);
            leftWing.castShadow = true;
            spaceshipGroup.add(leftWing);
            
            // Motor/propulsor
            const engineGeometry = new THREE.CylinderGeometry(0.15, 0.2, 0.5, 8);
            const engineMaterial = new THREE.MeshPhongMaterial({ color: 0xFF5722 });
            const engine = new THREE.Mesh(engineGeometry, engineMaterial);
            engine.position.set(0, 0, -0.8);
            engine.rotation.x = Math.PI / 2;
            engine.castShadow = true;
            spaceshipGroup.add(engine);
            
            spaceship = spaceshipGroup;
            spaceship.position.set(0, 0, 0); // Centrado en la pantalla
            scene.add(spaceship);
        }
        
        function createLights() {
            // Luz ambiental más tenue para ambiente más dramático
            const ambientLight = new THREE.AmbientLight(0x202040, 0.3);
            scene.add(ambientLight);

            // Luz direccional principal
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.7);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // Luz roja siniestra para los dientes
            const redLight = new THREE.PointLight(0xff0000, 0.5, 20);
            redLight.position.set(0, 0, -10);
            scene.add(redLight);

            // Luz azul fría para contraste
            const blueLight = new THREE.PointLight(0x0066ff, 0.3, 15);
            blueLight.position.set(-5, 5, 5);
            scene.add(blueLight);
        }
        
        function createStars() {
            // Estrellas principales
            const starGeometry = new THREE.BufferGeometry();
            const starMaterial = new THREE.PointsMaterial({
                color: 0xFFFFFF,
                size: 0.2,
                transparent: true,
                opacity: 0.8
            });

            const starVertices = [];
            for (let i = 0; i < 800; i++) {
                const x = (Math.random() - 0.5) * 200;
                const y = (Math.random() - 0.5) * 200;
                const z = (Math.random() - 0.5) * 200;
                starVertices.push(x, y, z);
            }

            starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
            const stars = new THREE.Points(starGeometry, starMaterial);
            scene.add(stars);

            // Partículas de polvo espacial
            const dustGeometry = new THREE.BufferGeometry();
            const dustMaterial = new THREE.PointsMaterial({
                color: 0x4488ff,
                size: 0.05,
                transparent: true,
                opacity: 0.3
            });

            const dustVertices = [];
            for (let i = 0; i < 500; i++) {
                const x = (Math.random() - 0.5) * 100;
                const y = (Math.random() - 0.5) * 100;
                const z = (Math.random() - 0.5) * 100;
                dustVertices.push(x, y, z);
            }

            dustGeometry.setAttribute('position', new THREE.Float32BufferAttribute(dustVertices, 3));
            const dust = new THREE.Points(dustGeometry, dustMaterial);
            scene.add(dust);
        }
        
        function createObstacle() {
            const obstacleGroup = new THREE.Group();

            // Intercalar altura: pegados arriba o abajo
            let yPosition, isTop;
            if (lastObstacleHeight <= 0) {
                yPosition = 0; // Posición base para diente superior
                isTop = true;
                lastObstacleHeight = 1;
            } else {
                yPosition = 0; // Posición base para diente inferior
                isTop = false;
                lastObstacleHeight = -1;
            }

            // Crear diente de cocodrilo más pequeño (con espacio para pasar)
            // Tamaño base que se reduce con la dificultad
            const baseToothHeight = 6 - (difficultyLevel * 0.3); // Se hace más pequeño cada nivel
            const baseToothWidth = 2.5 - (difficultyLevel * 0.1); // Ancho también se reduce

            const toothHeight = Math.max(baseToothHeight, 3); // Mínimo 3 unidades
            const toothWidth = Math.max(baseToothWidth, 1.5); // Mínimo 1.5 unidades

            // Crear diente de cocodrilo más realista
            const toothGroup = new THREE.Group();

            // Diente principal (forma más alargada y puntiaguda)
            let mainGeometry;
            if (isTop) {
                // Diente superior (punta hacia abajo)
                mainGeometry = new THREE.ConeGeometry(toothWidth * 0.3, toothHeight, 6);
                yPosition = 4 - (toothHeight / 2);
            } else {
                // Diente inferior (punta hacia arriba)
                mainGeometry = new THREE.ConeGeometry(toothWidth * 0.3, toothHeight, 6);
                yPosition = -4 + (toothHeight / 2);
            }

            // Material principal del diente (más realista)
            const toothMaterial = new THREE.MeshPhongMaterial({
                color: 0xFFFFFF, // Blanco como diente real
                shininess: 30,
                specular: 0x222222
            });
            const mainTooth = new THREE.Mesh(mainGeometry, toothMaterial);
            mainTooth.castShadow = true;
            mainTooth.receiveShadow = true;

            // Base del diente (más ancha, como raíz)
            const baseGeometry = new THREE.ConeGeometry(toothWidth, toothHeight * 0.4, 6);
            const baseMaterial = new THREE.MeshPhongMaterial({
                color: 0xDDDDDD, // Gris claro para la base
                shininess: 10
            });
            const baseTooth = new THREE.Mesh(baseGeometry, baseMaterial);
            baseTooth.castShadow = true;
            baseTooth.receiveShadow = true;

            // Posicionar base y punta
            if (isTop) {
                mainTooth.position.y = -toothHeight * 0.2; // Punta hacia abajo
                baseTooth.position.y = toothHeight * 0.3; // Base arriba
                baseTooth.rotation.x = Math.PI; // Invertir base
            } else {
                mainTooth.position.y = toothHeight * 0.2; // Punta hacia arriba
                mainTooth.rotation.x = Math.PI;
                baseTooth.position.y = -toothHeight * 0.3; // Base abajo
            }

            // Agregar brillo amenazante en la punta
            const tipGeometry = new THREE.SphereGeometry(toothWidth * 0.1, 8, 8);
            const tipMaterial = new THREE.MeshPhongMaterial({
                color: 0xFFFFFF,
                shininess: 100,
                specular: 0x666666,
                emissive: 0x111111 // Brillo sutil
            });
            const tip = new THREE.Mesh(tipGeometry, tipMaterial);

            if (isTop) {
                tip.position.y = -toothHeight * 0.45; // En la punta inferior
            } else {
                tip.position.y = toothHeight * 0.45; // En la punta superior
            }

            // Ensamblar el diente completo
            toothGroup.add(baseTooth);
            toothGroup.add(mainTooth);
            toothGroup.add(tip);

            obstacleGroup.add(toothGroup);

            // Posición inicial
            obstacleGroup.position.set(0, yPosition, -20);

            // Propiedades para obstáculos ESTÁTICOS (sin efectos visuales)
            obstacleGroup.userData = {
                isTop: isTop,
                isStatic: true, // Marcador para obstáculos estáticos
                toothHeight: toothHeight, // Guardar altura para colisiones
                toothWidth: toothWidth // Guardar ancho para colisiones
            };

            scene.add(obstacleGroup);
            obstacles.push(obstacleGroup);
        }

        function setupEventListeners() {
            // Mouse events
            document.addEventListener('mousedown', () => {
                if (gameRunning) {
                    mousePressed = true;
                }
            });

            document.addEventListener('mouseup', () => {
                mousePressed = false;
            });

            // Resize event
            window.addEventListener('resize', onWindowResize);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function updateSpaceship() {
            if (!gameRunning) return;

            // Física más realista: cuesta subir, caída suave
            if (mousePressed) {
                spaceshipVelocityY += SPACESHIP_SPEED; // Subida lenta (cuesta levantarla)
            } else {
                spaceshipVelocityY -= GRAVITY; // Caída suave
            }

            // Limitar velocidad (subida lenta, caída moderada)
            spaceshipVelocityY = Math.max(-0.15, Math.min(0.08, spaceshipVelocityY));

            // Actualizar posición directamente
            spaceship.position.y += spaceshipVelocityY;

            // Limitar posición en pantalla
            spaceship.position.y = Math.max(-4, Math.min(4, spaceship.position.y));

            // Rotación sutil basada en velocidad
            spaceship.rotation.z = -spaceshipVelocityY * 3;

            // Efectos visuales en la nave (pulsación sutil)
            const time = Date.now() * 0.005;
            spaceship.scale.setScalar(1 + Math.sin(time) * 0.05);
        }

        function updateObstacles() {
            if (!gameRunning) return;

            // Actualizar obstáculos existentes
            for (let i = obstacles.length - 1; i >= 0; i--) {
                const obstacle = obstacles[i];

                // Mover obstáculo hacia la cámara (efecto de acercamiento)
                obstacle.position.z += OBSTACLE_SPEED * gameSpeed;

                // Los obstáculos son ESTÁTICOS - sin efectos visuales
                // Solo mantener orientación correcta del diente
                if (obstacle.children && obstacle.children.length > 0) {
                    const tooth = obstacle.children[0];
                    if (tooth && !obstacle.userData.isTop) {
                        // Asegurar que el diente inferior mantenga orientación hacia arriba
                        tooth.rotation.x = Math.PI;
                    }
                }

                // Eliminar obstáculos que pasaron la cámara
                if (obstacle.position.z > 15) {
                    scene.remove(obstacle);
                    obstacles.splice(i, 1);
                    score += 10;
                    obstaclesPassed++;

                    // Aumentar dificultad cada 6 obstáculos (3 arriba + 3 abajo)
                    if (obstaclesPassed % 6 === 0) {
                        difficultyLevel++;
                        gameSpeed *= 1.2; // Aumentar velocidad x1.2 cada nivel
                        console.log(`¡Nivel ${difficultyLevel}! Velocidad: ${gameSpeed.toFixed(2)} - Dientes más pequeños!`);
                    }
                }

                // Detectar colisiones cuando el obstáculo está cerca de la nave
                if (obstacle.position.z > 9 && obstacle.position.z < 11) {
                    // Verificar colisión usando posiciones directas
                    const shipPos = spaceship.position;
                    const obstaclePos = obstacle.position;
                    const userData = obstacle.userData;

                    // Distancia en X e Y (ignorar Z para colisión 2D efectiva)
                    const distanceX = Math.abs(shipPos.x - obstaclePos.x);
                    const distanceY = Math.abs(shipPos.y - obstaclePos.y);

                    // Colisión basada en el tamaño real del diente (más pequeño)
                    const collisionWidth = userData.toothWidth || 1.5;
                    const collisionHeight = (userData.toothHeight || 3) / 2;

                    if (distanceX < collisionWidth && distanceY < collisionHeight) {
                        console.log("¡NAVE TOCÓ EL DIENTE! Nivel:", difficultyLevel, "Tamaño diente:", collisionWidth.toFixed(2), "x", collisionHeight.toFixed(2));
                        gameOver();
                    }
                }
            }

            // Crear nuevos obstáculos
            if (Math.random() < SPAWN_RATE * gameSpeed) {
                createObstacle();
            }
        }

        // Función simplificada de colisión (ya no se usa, pero la mantengo por compatibilidad)
        function checkCollisionWithHitbox(ship, hitbox, obstaclePosition) {
            return false; // Ahora usamos detección de distancia directa
        }

        function updateUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('speed').textContent = `${gameSpeed.toFixed(1)} (Nivel ${difficultyLevel})`;
            document.getElementById('obstacles').textContent = `${obstaclesPassed} (Espacio: ${(8 - difficultyLevel * 0.6).toFixed(1)}u)`;
        }

        function gameOver() {
            gameRunning = false;

            // Efecto visual de colisión
            spaceship.material = spaceship.children[0].material;
            if (spaceship.children[0].material) {
                spaceship.children[0].material.color.setHex(0xFF0000); // Nave roja al chocar
            }

            document.getElementById('finalScore').textContent = score;
            document.getElementById('gameOver').style.display = 'block';

            console.log("GAME OVER - La nave tocó un diente!");
        }

        function restartGame() {
            // Resetear variables
            gameRunning = true;
            score = 0;
            gameSpeed = 1;
            spaceshipVelocityY = 0;
            obstaclesPassed = 0;
            difficultyLevel = 1;

            // Limpiar obstáculos
            obstacles.forEach(obstacle => scene.remove(obstacle));
            obstacles = [];

            // Resetear posición de la nave
            spaceship.position.set(0, 0, 0);
            spaceship.rotation.z = 0;
            spaceship.scale.setScalar(1);
            spaceshipVelocityY = 0;
            lastObstacleHeight = 0;

            // Restaurar color original de la nave
            if (spaceship.children[0] && spaceship.children[0].material) {
                spaceship.children[0].material.color.setHex(0x4CAF50); // Verde original
            }

            // Ocultar pantalla de game over
            document.getElementById('gameOver').style.display = 'none';
        }

        function animate() {
            requestAnimationFrame(animate);

            updateSpaceship();
            updateObstacles();
            updateUI();

            renderer.render(scene, camera);
        }

        // Inicializar el juego cuando se carga la página
        window.addEventListener('load', init);
    </script>
</body>
</html>
